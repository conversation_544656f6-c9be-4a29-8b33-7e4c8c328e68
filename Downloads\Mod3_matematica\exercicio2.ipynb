{"cells": [{"cell_type": "markdown", "id": "74aa8b44", "metadata": {}, "source": ["# Exercício 2 — Teste de Hipótese para Validação do Modelo\n", "Valide se a **média da PREVISÃO DE VENDAS** difere da **média de VENDAS REAIS**.\n", "Assuma que **VENDAS REAIS** é a **população conhecida** (usa-se μ e σ populacionais)."]}, {"cell_type": "markdown", "id": "8c9f2c0a", "metadata": {}, "source": ["## (a) Hipóteses\n", "- **H0:** μ_prev = μ_real  \n", "- **Ha:** μ_prev ≠ μ_real"]}, {"cell_type": "markdown", "id": "9e65ec32", "metadata": {}, "source": ["## (b) <PERSON><PERSON> c<PERSON> (α = 5%, teste bicaudal)\n", "- **Normal padrão**, porque o desvio‑padrão populacional é conhecido.\n", "- **Z_crit = ±1,96**"]}, {"cell_type": "markdown", "id": "cb5225c5", "metadata": {}, "source": ["## (c) Estatística de teste\n", "<PERSON><PERSON>ar dados, calcular μ_real, σ_real (populacionais) e a média amostral das previsões."]}, {"cell_type": "code", "execution_count": null, "id": "4b35c55a", "metadata": {}, "outputs": [{"data": {"text/plain": ["(np.float64(135.28),\n", " np.float64(4.193041855264504),\n", " 100,\n", " np.float64(134.77),\n", " 'VENDAS REAIS',\n", " 'PREVISAO DE VENDAS',\n", " ',',\n", " ',')"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["%pip install pandas numpy scipy\n", "import pandas as pd\n", "import numpy as np\n", "\n", "# Leitura robusta do CSV (variações de separador e decimal)\n", "def try_read(path):\n", "    for sep in [';', ',', '\\t']:\n", "        for dec in [',', '.']:\n", "            try:\n", "                df = pd.read_csv(path, sep=sep, decimal=dec, engine=\"python\")\n", "                if df.shape[1] >= 2:\n", "                    return df, sep, dec\n", "            except Exception:\n", "                continue\n", "    return pd.read_csv(path), ',', '.'\n", "\n", "df, used_sep, used_dec = try_read(\"DadosPágina1.csv\")\n", "df.columns = [c.strip().upper() for c in df.columns]\n", "\n", "# Detectar colunas pelos nomes\n", "col_real = [c for c in df.columns if \"VENDAS REAIS\" in c][0]\n", "col_prev = [c for c in df.columns if \"PREVISAO DE VENDAS\" in c][0]\n", "\n", "# Coagir a numérico\n", "df[col_real] = pd.to_numeric(df[col_real], errors='coerce')\n", "df[col_prev] = pd.to_numeric(df[col_prev], errors='coerce')\n", "df = df.dropna(subset=[col_real, col_prev]).reset_index(drop=True)\n", "\n", "real = df[col_real].values\n", "prev = df[col_prev].values\n", "\n", "mu_real = real.mean()\n", "sigma_real = real.std(ddof=0)   # populacional\n", "n = len(prev)\n", "xbar_prev = prev.mean()\n", "\n", "mu_real, sigma_real, n, xbar_prev, col_real, col_prev, used_sep, used_dec"]}, {"cell_type": "markdown", "id": "eec45275", "metadata": {}, "source": [" calcula **Z** e o **p‑valor bicaudal**."]}, {"cell_type": "code", "execution_count": 16, "id": "267733f6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== CÁLCULOS INTERMEDIÁRIOS ===\n", "<PERSON><PERSON> (σ/√n): 0.4193\n", "Diferença das médias (x̄ - μ): -0.51\n", "\n", "=== RESULTADOS DO TESTE ===\n", "Z crítico (α=5%): ±1.960\n", "Z calculado: -1.216\n", "p-valor (bicaudal): 0.2239\n"]}], "source": ["from math import sqrt\n", "from scipy.stats import norm\n", "\n", "# Parâmetros do teste\n", "alpha = 0.05\n", "z_crit = norm.ppf(1 - alpha/2)\n", "\n", "# Cálculo da estatística Z\n", "# Z = (x̄ - μ) / (σ / √n)\n", "erro_padrao = sigma_real / sqrt(n)\n", "z_stat = (xbar_prev - mu_real) / erro_padrao\n", "\n", "# Cálculo do p-valor bicaudal\n", "p_value = 2 * (1 - norm.cdf(abs(z_stat)))\n", "\n", "print(f\"\\n=== CÁLCULOS INTERMEDIÁRIOS ===\")\n", "print(f\"<PERSON>rro padrão (σ/√n): {erro_padrao:.4f}\")\n", "print(f\"Diferença das médias (x̄ - μ): {xbar_prev - mu_real:.2f}\")\n", "print(f\"\\n=== RESULTADOS DO TESTE ===\")\n", "print(f\"Z crítico (α=5%): ±{z_crit:.3f}\")\n", "print(f\"Z calculado: {z_stat:.3f}\")\n", "print(f\"p-valor (bicaudal): {p_value:.4f}\")"]}, {"cell_type": "markdown", "id": "8c32bfa9", "metadata": {}, "source": ["## (d) <PERSON><PERSON><PERSON>ão\n", "Se |Z| > 1,96 (ou p‑valor < 0,05), **rejeita o H0**. <PERSON><PERSON><PERSON> con<PERSON>, **não rejeita o H0**."]}, {"cell_type": "code", "execution_count": null, "id": "8c24ba80", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== RESULTADOS DO TESTE DE HIPÓTESE ===\n", "Média real (μ): 135.28\n", "Média das previsões (x̄): 134.77\n", "<PERSON><PERSON> populacional (σ): 4.193\n", "<PERSON><PERSON><PERSON> (n): 100\n", "\n", "Estatística Z calculada: -1.216\n", "Z crítico (α=5%): ±1.960\n", "p-valor (bicaudal): 0.2239\n", "\n", "=== CONCLUSÃO ===\n", "Critério do valor crítico: Não rejeitamos H0: não há diferença significativa entre as médias.\n", "Critério do p-valor: Não rejeitamos H0 (p-valor ≥ α).\n", "\n", "Interpretação: O modelo de previsão não apresenta viés significativo.\n", "A diferença entre as médias (0.51) pode ser atribuída à variação aleatória.\n"]}], "source": ["# Conclusão do teste de hipótese\n", "# Verificamos se |Z| > Z_c<PERSON><PERSON><PERSON><PERSON> ou se p-valor < α\n", "\n", "print(f\"\\n=== RESULTADOS DO TESTE DE HIPÓTESE ===\")\n", "print(f\"Média real (μ): {mu_real:.2f}\")\n", "print(f\"Média das previsões (x̄): {xbar_prev:.2f}\")\n", "print(f\"Desvio padrão populacional (σ): {sigma_real:.3f}\")\n", "print(f\"<PERSON><PERSON><PERSON> da amostra (n): {n}\")\n", "print(f\"\\nEstatística Z calculada: {z_stat:.3f}\")\n", "print(f\"Z crítico (α=5%): ±{z_crit:.3f}\")\n", "print(f\"p-valor (bicaudal): {p_value:.4f}\")\n", "\n", "# Decisão baseada no valor crítico\n", "if abs(z_stat) > z_crit:\n", "    decision_z = \"Re<PERSON>ita H0: a média das previsões é significativamente diferente da média real.\"\n", "else:\n", "    decision_z = \"Não rejeita H0: não há diferença significativa entre as médias.\"\n", "\n", "# Decis<PERSON> baseada no p-valor\n", "if p_value < alpha:\n", "    decision_p = \"Rejeita H0 (p-valor < α).\"\n", "else:\n", "    decision_p = \"Não rejeita H0 (p-valor ≥ α).\"\n", "\n", "print(f\"\\n=== CONCLUSÃO ===\")\n", "print(f\"Critério do valor crítico: {decision_z}\")\n", "print(f\"Critério do p-valor: {decision_p}\")\n", "\n", "# Interpretação prática\n", "if abs(z_stat) > z_crit:\n", "    print(f\"\\nInterpretação: O modelo de previsão apresenta viés significativo.\")\n", "    print(f\"A diferença entre as médias ({abs(mu_real - xbar_prev):.2f}) é estatisticamente significativa.\")\n", "else:\n", "    print(f\"\\nInterpretação: O modelo de previsão não apresenta viés significativo.\")\n", "    print(\"A diferença entre as médias pode ser atribuída à variação aleatória.\")"]}, {"cell_type": "markdown", "id": "334d8cbe", "metadata": {}, "source": ["> Observação: Usamos **σ_real** (populacional) por definição do enunciado, logo a estatística de teste é **Z** (e não t)."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}