import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# Carregar dados
df = pd.read_csv("DadosPágina1.csv", sep=",", decimal=",", engine="python")
df.columns = [c.strip().upper() for c in df.columns]

col_real = "VENDAS REAIS"
col_prev = "PREVISÃO DE VENDAS"

df[col_real] = pd.to_numeric(df[col_real], errors='coerce')
df[col_prev] = pd.to_numeric(df[col_prev], errors='coerce')
df = df.dropna(subset=[col_real, col_prev]).reset_index(drop=True)

# Estatísticas populacionais (ddof=0)
mean_real = df[col_real].mean()
std_real = df[col_real].std(ddof=0)
mean_prev = df[col_prev].mean()
std_prev = df[col_prev].std(ddof=0)

print("Média VENDAS REAIS (pop.):", round(mean_real, 1))
print("Desvio-padrão VENDAS REAIS (pop.):", round(std_real, 1))
print("Média PREVISÃO DE VENDAS (pop.):", round(mean_prev, 1))
print("Desvio-padrão PREVISÃO DE VENDAS (pop.):", round(std_prev, 1))

import numpy as np
import matplotlib.pyplot as plt

values = df[col_real].values
bins = 10
counts, bin_edges, _ = plt.hist(values, bins=bins, edgecolor='black')
plt.xlabel("VENDAS REAIS"); plt.ylabel("Frequência")
plt.title("Histograma de VENDAS REAIS (10 bins) + Curva Gaussiana")

# Curva gaussiana com parâmetros do item (a)
x = np.linspace(values.min(), values.max(), 500)
if std_real > 0:
    pdf = (1.0/(std_real*np.sqrt(2*np.pi))) * np.exp(-0.5*((x-mean_real)/std_real)**2)
    bin_width = (bin_edges.max() - bin_edges.min()) / bins
    plt.plot(x, pdf * len(values) * bin_width, linewidth=2)

plt.tight_layout()
plt.show()

from math import erf, sqrt
import pandas as pd
import numpy as np

# Carregar dados
df = pd.read_csv("DadosPágina1.csv", sep=",", decimal=",", engine="python")
df.columns = [c.strip().upper() for c in df.columns]

col_prev = "PREVISAO DE VENDAS"
df[col_prev] = pd.to_numeric(df[col_prev], errors='coerce')
df = df.dropna(subset=[col_prev]).reset_index(drop=True)

mean_prev = df[col_prev].mean()
std_prev = df[col_prev].std(ddof=0)

def normal_cdf(x, mu, sigma):
    if sigma <= 0:
        return 0.0 if x < mu else 1.0 if x > mu else 0.5
    z = (x - mu) / (sigma * sqrt(2))
    return 0.5 * (1 + erf(z))

p = normal_cdf(130.0, mean_prev, std_prev)
print("Média PREVISÃO (μ):", round(mean_prev, 1))
print("Desvio-padrão PREVISÃO (σ):", round(std_prev, 1))
print("P(X < 130) =", round(p, 4), "=>", round(p*100, 2), "%")