#!/usr/bin/env python3
"""
Teste para verificar se o exercicio2.ipynb funciona corretamente
após as correções realizadas.
"""

import pandas as pd
import numpy as np
from math import sqrt
from scipy.stats import norm

def test_exercicio2():
    """Testa o código do exercício 2 para verificar se funciona corretamente."""
    
    print("=== TESTE DO EXERCÍCIO 2 ===")
    
    # Leitura robusta do CSV (mesmo código do notebook)
    def try_read(path):
        for sep in [';', ',', '\t']:
            for dec in [',', '.']:
                try:
                    df = pd.read_csv(path, sep=sep, decimal=dec, engine="python")
                    if df.shape[1] >= 2:
                        return df, sep, dec
                except Exception:
                    continue
        return pd.read_csv(path), ',', '.'

    # Carregamento dos dados
    try:
        df, used_sep, used_dec = try_read("DadosPágina1.csv")
        df.columns = [c.strip().upper() for c in df.columns]
        print("✓ Dados carregados com sucesso")
    except Exception as e:
        print(f"✗ Erro ao carregar dados: {e}")
        return False

    # Detectar colunas pelos nomes
    try:
        col_real = [c for c in df.columns if "VENDAS REAIS" in c][0]
        col_prev = [c for c in df.columns if "PREVISAO DE VENDAS" in c][0]
        print(f"✓ Colunas detectadas: {col_real}, {col_prev}")
    except Exception as e:
        print(f"✗ Erro ao detectar colunas: {e}")
        return False

    # Coagir a numérico
    try:
        df[col_real] = pd.to_numeric(df[col_real], errors='coerce')
        df[col_prev] = pd.to_numeric(df[col_prev], errors='coerce')
        df = df.dropna(subset=[col_real, col_prev]).reset_index(drop=True)
        
        real = df[col_real].values
        prev = df[col_prev].values
        print("✓ Dados convertidos para numérico")
    except Exception as e:
        print(f"✗ Erro na conversão numérica: {e}")
        return False

    # Cálculos estatísticos
    try:
        mu_real = real.mean()
        sigma_real = real.std(ddof=0)   # populacional
        n = len(prev)
        xbar_prev = prev.mean()
        
        print(f"✓ Estatísticas calculadas:")
        print(f"  - Média real (μ): {mu_real:.2f}")
        print(f"  - Desvio padrão populacional (σ): {sigma_real:.3f}")
        print(f"  - Tamanho da amostra (n): {n}")
        print(f"  - Média das previsões (x̄): {xbar_prev:.2f}")
    except Exception as e:
        print(f"✗ Erro nos cálculos estatísticos: {e}")
        return False

    # Teste de hipótese
    try:
        alpha = 0.05
        z_crit = norm.ppf(1 - alpha/2)
        
        erro_padrao = sigma_real / sqrt(n)
        z_stat = (xbar_prev - mu_real) / erro_padrao
        p_value = 2 * (1 - norm.cdf(abs(z_stat)))
        
        print(f"✓ Teste de hipótese calculado:")
        print(f"  - Z crítico (α=5%): ±{z_crit:.3f}")
        print(f"  - Z calculado: {z_stat:.3f}")
        print(f"  - p-valor: {p_value:.4f}")
        print(f"  - Erro padrão: {erro_padrao:.4f}")
    except Exception as e:
        print(f"✗ Erro no teste de hipótese: {e}")
        return False

    # Conclusão
    try:
        if abs(z_stat) > z_crit:
            decision = "Rejeitamos H0: a média das previsões é significativamente diferente da média real."
        else:
            decision = "Não rejeitamos H0: não há diferença significativa entre as médias."
        
        print(f"✓ Conclusão: {decision}")
        
        # Verificação de consistência
        decision_p = "Rejeitamos H0" if p_value < alpha else "Não rejeitamos H0"
        decision_z = "Rejeitamos H0" if abs(z_stat) > z_crit else "Não rejeitamos H0"
        
        if decision_p == decision_z:
            print("✓ Decisões por p-valor e valor crítico são consistentes")
        else:
            print("✗ Inconsistência entre decisões por p-valor e valor crítico")
            return False
            
    except Exception as e:
        print(f"✗ Erro na conclusão: {e}")
        return False

    print("\n=== TESTE CONCLUÍDO COM SUCESSO ===")
    return True

if __name__ == "__main__":
    test_exercicio2()
